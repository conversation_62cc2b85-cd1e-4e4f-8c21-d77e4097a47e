<?php
declare(strict_types=1);

namespace App\Core\Services\TestFile;

use App\Model\TestFile\FileSync;
use App\Model\TestFile\ReorganizeTask;
use App\Model\TestFile\Product;
use App\Model\TestFile\TestRecord;
use Carbon\Carbon;
use Hyperf\DbConnection\Db;
use Hyperf\Logger\LoggerFactory;
use Psr\Log\LoggerInterface;
use Hyperf\Contract\ConfigInterface;

class ReorganizeService
{
    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @var ConfigInterface
     */
    protected $config;

    protected $sourceDir;
    protected $targetDir;

    public function __construct(LoggerFactory $loggerFactory, ConfigInterface $config)
    {
        $this->logger = $loggerFactory->get('reorganize');
        $this->config = $config;

        // 从配置文件读取目录设置
        $this->sourceDir = $this->config->get('test_file.test_file.source_dir', '/tmp/test');
        $this->targetDir = $this->config->get('test_file.test_file.target_dir', '/tmp/test2');
    }

    /**
     * 执行目录重排任务
     */
    public function execute(array $options = []): array
    {
        $task = $this->createTask($options);

        try {
            $this->updateTaskStatus($task->id, 1); // 处理中

            $sourceDir = $options['source_dir'] ?? $this->sourceDir;
            $targetDir = $options['target_dir'] ?? $this->targetDir;

            $this->logger->info('开始执行重排任务', [
                'task_id' => $task->id,
                'source_dir' => $sourceDir,
                'target_dir' => $targetDir
            ]);

            // 创建目标目录
            if (!is_dir($targetDir)) {
                mkdir($targetDir, 0755, true);
                $this->logger->info('创建目标目录', ['dir' => $targetDir]);
            }

            $files = $this->scanDirectory($sourceDir);
            $processedCount = 0;

            $this->logger->info('扫描到文件', ['count' => count($files)]);

            foreach ($files as $file) {
                if ($this->processFile($file, $sourceDir, $targetDir)) {
                    $processedCount++;
                }
            }

            $this->updateTaskStatus($task->id, 2, $processedCount); // 完成

            $this->logger->info('重排任务完成', [
                'task_id' => $task->id,
                'processed' => $processedCount,
                'total' => count($files)
            ]);
            return [
                'task_id' => $task->id,
                'processed' => $processedCount,
                'total' => count($files)
            ];

        } catch (\Exception $e) {
            $this->logger->info('重排任务失败', [
                'task_id' => $task->id,
                'error' => $e->getMessage()
            ]);

            $this->updateTaskStatus($task->id, 3, 0, $e->getMessage()); // 失败
            throw $e;
        }
    }

    /**
     * 扫描目录获取文件列表
     */
    protected function scanDirectory(string $dir): array
    {
        if (!is_dir($dir)) {
            $this->logger->warning('目录不存在', ['dir' => $dir]);
            return [];
        }

        $files = [];
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($dir, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::SELF_FIRST
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $files[] = $file->getPathname();
            }
        }

        return $files;
    }

    /**
     * 处理单个文件
     */
    protected function processFile(string $filePath, string $sourceBase, string $targetBase): bool
    {
        // 解析文件路径
        $pathInfo = $this->parseFilePath($filePath, $sourceBase);
        if (!$pathInfo) {
            $this->logger->warning('无法解析文件路径', ['file' => $filePath]);
            return false;
        }

        // 检查是否已处理
        $existing = FileSync::where('src_path', $filePath)
            ->where('sync_status', '!=', 3)
            ->first();

        if ($existing) {
            $this->logger->warning('文件已存在', ['file' => $filePath]);
            return false;
        }

        // 构建目标路径
        $targetPath = $this->buildTargetPath($pathInfo, $targetBase);

        // 创建目标目录
        $targetDir = dirname($targetPath);
        if (!is_dir($targetDir)) {
            mkdir($targetDir, 0755, true);
        }

        // 复制文件
        if (!copy($filePath, $targetPath)) {
            $this->logger->error('文件复制失败', [
                'source' => $filePath,
                'target' => $targetPath
            ]);
            return false;
        }

        // 计算文件信息
        $fileInfo = $this->getFileInfo($filePath);

        // 写入数据库
        $record = new FileSync();
        $record->product = $pathInfo['product'];
        $record->sn = $pathInfo['sn'];
        $record->date_folder = $pathInfo['date_folder'];
        $record->test_datetime = $pathInfo['test_datetime'];
        $record->test_type = $pathInfo['test_type'];
        $record->filename = $pathInfo['filename'];
        $record->file_size = $fileInfo['size'];
        $record->file_mtime = Carbon::createFromTimestamp($fileInfo['mtime']);
        $record->src_path = $filePath;
        $record->dst_path = $targetPath;
        $record->file_md5 = $fileInfo['md5'];
        $record->sync_status = 2; // 已重排
        $record->save();

        // 解析测试文件内容（如果需要）
        $this->parseTestFile($targetPath, $pathInfo);

        $this->logger->info('文件处理成功', [
            'file' => $pathInfo['filename'],
            'product' => $pathInfo['product'],
            'sn' => $pathInfo['sn']
        ]);

        return true;
    }

    /**
     * 解析文件路径
     */
    protected function parseFilePath(string $filePath, string $baseDir): ?array
    {
        $relativePath = str_replace($baseDir . '/', '', $filePath);
        $parts = explode('/', $relativePath);

        // 期望格式: 日期/产品/SN/测试时间/[测试类型/]文件名
        if (count($parts) < 5) {
            return null;
        }

        $dateFolder = $parts[0];
        $product = $parts[1];
        $sn = $parts[2];
        $testDatetime = $parts[3];

        // 判断是否有测试类型目录
        if (count($parts) == 6) {
            $testType = $parts[4];
            $filename = $parts[5];
        } else {
            $testType = null;
            $filename = $parts[4];
        }

        return [
            'date_folder' => $dateFolder,
            'product' => $product,
            'sn' => $sn,
            'test_datetime' => $testDatetime,
            'test_type' => $testType,
            'filename' => $filename,
            'relative_path' => $relativePath
        ];
    }

    /**
     * 构建目标路径
     */
    protected function buildTargetPath(array $pathInfo, string $targetBase): string
    {
        $path = $targetBase . '/' . $pathInfo['product'] . '/' .
            $pathInfo['sn'] . '/' . $pathInfo['test_datetime'];

        if ($pathInfo['test_type']) {
            $path .= '/' . $pathInfo['test_type'];
        }

        $path .= '/' . $pathInfo['filename'];

        return $path;
    }

    /**
     * 获取文件信息
     */
    protected function getFileInfo(string $filePath): array
    {
        return [
            'size' => filesize($filePath),
            'mtime' => filemtime($filePath),
            'md5' => md5_file($filePath)
        ];
    }

    /**
     * 创建任务记录
     */
    protected function createTask(array $options): ReorganizeTask
    {
        $task = new ReorganizeTask();
        $task->task_type = $options['task_type'] ?? 'manual';
        $task->source_dir = $options['source_dir'] ?? $this->sourceDir;
        $task->target_dir = $options['target_dir'] ?? $this->targetDir;
        $task->status = 0;
        $task->save();

        return $task;
    }

    /**
     * 更新任务状态
     */
    protected function updateTaskStatus(int $taskId, int $status, int $processed = 0, ?string $error = null): void
    {
        $task = ReorganizeTask::find($taskId);
        if (!$task) {
            return;
        }

        $task->status = $status;

        if ($status === 1) {
            $task->started_at = Carbon::now();
        } elseif ($status === 2) {
            $task->completed_at = Carbon::now();
            $task->processed_files = $processed;
        } elseif ($status === 3) {
            $task->completed_at = Carbon::now();
            $task->error_message = $error;
        }

        $task->save();
    }

    /**
     * 获取任务列表
     */
    public function getTaskList(array $params): array
    {
        $page = (int)($params['page'] ?? 1);
        $pageSize = (int)($params['page_size'] ?? 20);

        $query = ReorganizeTask::query();

        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }

        $total = $query->count();
        $list = $query->orderBy('id', 'desc')
            ->forPage($page, $pageSize)
            ->get();

        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize
        ];
    }

    /**
     * 获取任务详情
     */
    public function getTaskById(int $id): ?array
    {
        $task = ReorganizeTask::find($id);
        return $task ? $task->toArray() : null;
    }

    /**
     * 解析测试文件内容
     */
    protected function parseTestFile(string $filePath, array $pathInfo): void
    {
        $filename = basename($filePath);

        // 确保产品存在
        $product = Product::where('product_name', $pathInfo['product'])->first();
        if (!$product) {
            $product = new Product();
            $product->product_name = $pathInfo['product'];
            $product->status = 1;
            $product->save();
        }

        // 获取或创建测试记录
        $testRecord = TestRecord::where('product_id', $product->id)
            ->where('sn', $pathInfo['sn'])
            ->where('test_datetime', $pathInfo['test_datetime'])
            ->first();

        if (!$testRecord) {
            $testRecord = new TestRecord();
            $testRecord->product_id = $product->id;
            $testRecord->sn = $pathInfo['sn'];
            $testRecord->test_date = Carbon::createFromFormat('Ymd', $pathInfo['date_folder'])->toDateString();
            $testRecord->test_datetime = $pathInfo['test_datetime'];
            $testRecord->file_count = 1;
            $testRecord->save();
        } else {
            $testRecord->file_count += 1;
            $testRecord->save();
        }

        // 根据文件名解析具体内容
        switch ($filename) {
            case 'agingTest_result.txt':
                // 解析老化测试结果
                $this->parseAgingTestResult($filePath, $testRecord->id);
                break;

            case 'factoryTest_result.txt':
                // 解析厂测结果
                $this->parseFactoryTestResult($filePath, $testRecord->id);
                break;

            case 'cpuid.txt':
                // 更新 CPU ID
                $cpuid = trim(file_get_contents($filePath));
                if ($cpuid) {
                    $testRecord->cpuid = $cpuid;
                    $testRecord->save();
                }
                break;
        }
    }

    /**
     * 解析老化测试结果
     */
    protected function parseAgingTestResult(string $filePath, int $testRecordId): void
    {
        $content = file_get_contents($filePath);

        // 更新测试记录
        TestRecord::where('id', $testRecordId)->update([
            'aging_test_result' => '完成'
        ]);

        // 这里可以进一步解析文件内容并保存详细信息
    }

    /**
     * 解析厂测结果
     */
    protected function parseFactoryTestResult(string $filePath, int $testRecordId): void
    {
        $content = file_get_contents($filePath);

        // 简单解析，获取测试结果
        $result = '未知';
        if (strpos($content, '厂测结果:成功') !== false) {
            $result = '成功';
        } elseif (strpos($content, '厂测结果:失败') !== false) {
            $result = '失败';
        }

        // 更新测试记录
        TestRecord::where('id', $testRecordId)->update([
            'factory_test_result' => $result
        ]);

        // 这里可以进一步解析文件内容并保存详细信息
    }

}
