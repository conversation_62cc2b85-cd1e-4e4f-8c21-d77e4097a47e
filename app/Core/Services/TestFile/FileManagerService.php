<?php
declare(strict_types=1);

namespace App\Core\Services\TestFile;

use App\Model\TestFile\FileSync;
use App\Model\TestFile\Product;
use App\Model\TestFile\TestRecord;
use Hyperf\DbConnection\Db;
use Hyperf\HttpServer\Contract\ResponseInterface;
use Carbon\Carbon;

class FileManagerService
{
    /**
     * 获取文件列表
     */
    public function getFileList(array $params): array
    {
        $page = (int) ($params['page'] ?? 1);
        $pageSize = (int) ($params['page_size'] ?? 20);
        
        $query = FileSync::query();
        
        // 添加筛选条件
        if (!empty($params['product'])) {
            $query->where('product', $params['product']);
        }
        
        if (!empty($params['sn'])) {
            $query->where('sn', $params['sn']);
        }
        
        if (!empty($params['test_type'])) {
            $query->where('test_type', $params['test_type']);
        }
        
        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            $query->whereBetween('created_at', [
                $params['start_date'] . ' 00:00:00',
                $params['end_date'] . ' 23:59:59'
            ]);
        }
        
        // 排除已删除的文件
        $query->where('sync_status', '!=', 3);
        
        $total = $query->count();
        $list = $query->orderBy('id', 'desc')
                      ->forPage($page, $pageSize)
                      ->get();
        
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize
        ];
    }
    
    /**
     * 获取文件详情
     */
    public function getFileById(int $id): ?array
    {
        $file = FileSync::find($id);
        return $file ? $file->toArray() : null;
    }
    
    /**
     * 删除文件
     */
    public function deleteFile(int $id): bool
    {
        $file = FileSync::find($id);
        
        if (!$file) {
            return false;
        }
        
        // 标记为已删除
        $file->sync_status = 3;
        $file->save();
        
        // 删除实际文件
        if (file_exists($file->dst_path)) {
            @unlink($file->dst_path);
        }
        
        return true;
    }
    
    /**
     * 下载文件
     */
    public function downloadFile(int $id, ResponseInterface $response)
    {
        $file = FileSync::find($id);
        
        if (!$file || !file_exists($file->dst_path)) {
            throw new \Exception('文件不存在');
        }
        
        // 设置下载响应头
        return $response->withHeader('Content-Type', 'application/octet-stream')
                       ->withHeader('Content-Disposition', 'attachment; filename="' . $file->filename . '"')
                       ->withHeader('Cache-Control', 'no-cache, private')
                       ->withBody(new \Hyperf\HttpMessage\Stream\SwooleFileStream($file->dst_path));
    }
    
    /**
     * 获取树形目录数据
     */
    public function getTreeData(string $node, int $depth): array
    {
        if (empty($node)) {
            // 获取根节点（产品列表）
            $products = Db::table('file_sync')
                ->select('product')
                ->where('sync_status', '!=', 3)
                ->groupBy('product')
                ->get();
            
            $result = [];
            foreach ($products as $product) {
                $count = Db::table('file_sync')
                    ->where('product', $product->product)
                    ->where('sync_status', '!=', 3)
                    ->count();
                    
                $result[] = [
                    'id' => $product->product,
                    'label' => $product->product,
                    'type' => 'product',
                    'children_count' => $count,
                    'isLeaf' => false
                ];
            }
            
            return $result;
        }
        
        // 解析节点路径
        $parts = explode('/', $node);
        
        if (count($parts) == 1) {
            // 获取SN列表
            $product = $parts[0];
            $sns = Db::table('file_sync')
                ->select('sn')
                ->where('product', $product)
                ->where('sync_status', '!=', 3)
                ->groupBy('sn')
                ->get();
            
            $result = [];
            foreach ($sns as $sn) {
                $count = Db::table('file_sync')
                    ->where('product', $product)
                    ->where('sn', $sn->sn)
                    ->where('sync_status', '!=', 3)
                    ->count();
                    
                $result[] = [
                    'id' => $product . '/' . $sn->sn,
                    'label' => $sn->sn,
                    'type' => 'sn',
                    'children_count' => $count,
                    'isLeaf' => false
                ];
            }
            
            return $result;
        }
        
        if (count($parts) == 2) {
            // 获取日期文件夹列表
            $product = $parts[0];
            $sn = $parts[1];
            
            $dates = Db::table('file_sync')
                ->select('test_datetime')
                ->where('product', $product)
                ->where('sn', $sn)
                ->where('sync_status', '!=', 3)
                ->groupBy('test_datetime')
                ->get();
            
            $result = [];
            foreach ($dates as $date) {
                $count = Db::table('file_sync')
                    ->where('product', $product)
                    ->where('sn', $sn)
                    ->where('test_datetime', $date->test_datetime)
                    ->where('sync_status', '!=', 3)
                    ->count();
                    
                $result[] = [
                    'id' => $product . '/' . $sn . '/' . $date->test_datetime,
                    'label' => $date->test_datetime,
                    'type' => 'date',
                    'children_count' => $count,
                    'isLeaf' => true
                ];
            }
            
            return $result;
        }
        
        return [];
    }
    
    /**
     * 获取统计数据
     */
    public function getStatistics(): array
    {
        $totalFiles = FileSync::where('sync_status', '!=', 3)->count();
        $totalSize = FileSync::where('sync_status', '!=', 3)->sum('file_size') ?? 0;
        $totalProducts = FileSync::where('sync_status', '!=', 3)
            ->select('product')
            ->groupBy('product')
            ->get()
            ->count();
        $totalSns = FileSync::where('sync_status', '!=', 3)
            ->select('sn')
            ->groupBy('sn')
            ->get()
            ->count();
        
        return [
            'total_files' => $totalFiles,
            'total_size' => $totalSize,
            'total_products' => $totalProducts,
            'total_sns' => $totalSns
        ];
    }
    
    /**
     * 获取产品列表
     */
    public function getProducts(int $page, int $pageSize): array
    {
        $query = Product::query();
        
        $total = $query->count();
        $list = $query->forPage($page, $pageSize)->get();
        
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize
        ];
    }
    
    /**
     * 创建产品
     */
    public function createProduct(array $data): array
    {
        $product = new Product();
        $product->product_name = $data['product_name'];
        $product->product_code = $data['product_code'] ?? null;
        $product->description = $data['description'] ?? null;
        $product->status = 1;
        $product->save();
        
        return $product->toArray();
    }
    
    /**
     * 删除产品
     */
    public function deleteProduct(int $id): bool
    {
        $product = Product::find($id);
        
        if (!$product) {
            return false;
        }
        
        $product->delete();
        
        return true;
    }
}
