<?php
declare(strict_types=1);

namespace App\Controller\TestFile;

use App\Controller\AbstractController;
use App\Core\Services\TestFile\FileManagerService;
use App\Core\Services\TestFile\ReorganizeService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\PostMapping;
use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\HttpServer\Contract\ResponseInterface;
use Carbon\Carbon;

/**
 * @Controller(prefix="/api/testfile")
 */
class TestFileController extends AbstractController
{
    /**
     * @Inject
     * @var FileManagerService
     */
    protected $fileService;
    
    /**
     * @Inject
     * @var ReorganizeService
     */
    protected $reorganizeService;
    
    /**
     * 获取文件列表
     * @GetMapping(path="files")
     */
    public function getFiles(RequestInterface $request)
    {
        $params = $request->all();
        
        try {
            $result = $this->fileService->getFileList($params);
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * 获取文件详情
     * @GetMapping(path="files/{id}")
     */
    public function getFileDetail(int $id)
    {
        try {
            $file = $this->fileService->getFileById($id);
            
            if (!$file) {
                return $this->error('文件不存在', 3001);
            }
            
            return $this->success($file);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * 删除文件
     * @DeleteMapping(path="files/{id}")
     */
    public function deleteFile(int $id)
    {
        try {
            $result = $this->fileService->deleteFile($id);
            
            if (!$result) {
                return $this->error('删除失败', 4001);
            }
            
            return $this->success(['deleted' => true]);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * 下载文件
     * @GetMapping(path="files/{id}/download")
     */
    public function downloadFile(int $id, ResponseInterface $response)
    {
        try {
            return $this->fileService->downloadFile($id, $response);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * 获取树形目录
     * @GetMapping(path="tree")
     */
    public function getTree(RequestInterface $request)
    {
        $node = $request->input('node', '');
        $depth = (int) $request->input('depth', 1);
        
        try {
            $result = $this->fileService->getTreeData($node, $depth);
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * 手动触发重排
     * @PostMapping(path="reorganize")
     */
    public function triggerReorganize(RequestInterface $request)
    {
        $params = $request->all();
        
        try {
            $result = $this->reorganizeService->execute($params);
            
            return $this->success([
                'task_id' => $result['task_id'],
                'message' => '重排任务已创建'
            ]);
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), 4001);
        }
    }
    
    /**
     * 获取重排任务列表
     * @GetMapping(path="reorganize/tasks")
     */
    public function getTasks(RequestInterface $request)
    {
        $params = $request->all();
        
        try {
            $result = $this->reorganizeService->getTaskList($params);
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * 获取任务详情
     * @GetMapping(path="reorganize/tasks/{id}")
     */
    public function getTaskDetail(int $id)
    {
        try {
            $task = $this->reorganizeService->getTaskById($id);
            
            if (!$task) {
                return $this->error('任务不存在', 3001);
            }
            
            return $this->success($task);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * 获取统计数据
     * @GetMapping(path="statistics")
     */
    public function getStatistics(RequestInterface $request)
    {
        try {
            $result = $this->fileService->getStatistics();
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * 获取产品列表
     * @GetMapping(path="products")
     */
    public function getProducts(RequestInterface $request)
    {
        $page = (int) $request->input('page', 1);
        $pageSize = (int) $request->input('page_size', 20);
        
        try {
            $result = $this->fileService->getProducts($page, $pageSize);
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * 添加产品
     * @PostMapping(path="products")
     */
    public function createProduct(RequestInterface $request)
    {
        $data = $request->all();
        
        try {
            $product = $this->fileService->createProduct($data);
            return $this->success($product);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * 删除产品
     * @DeleteMapping(path="products/{id}")
     */
    public function deleteProduct(int $id)
    {
        try {
            $result = $this->fileService->deleteProduct($id);
            
            if (!$result) {
                return $this->error('产品不存在', 3001);
            }
            
            return $this->success(['deleted' => true]);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * 返回成功响应
     */
    protected function success($data = [], string $message = 'success'): array
    {
        return [
            'code' => 0,
            'message' => $message,
            'data' => $data,
            'timestamp' => time()
        ];
    }
    
    /**
     * 返回错误响应
     */
    protected function error(string $message = 'error', int $code = 1): array
    {
        return [
            'code' => $code,
            'message' => $message,
            'data' => [],
            'timestamp' => time()
        ];
    }
}
