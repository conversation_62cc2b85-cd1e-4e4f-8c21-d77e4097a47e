<?php

    use Hyperf\Database\Schema\Schema;
    use Hyperf\Database\Schema\Blueprint;
    use Hyperf\Database\Migrations\Migration;

    class CreateTestCaseChangeTable extends Migration
    {
        /**
         * 指定数据库连接
         */
        protected $connection = 'tchip_redmine';

        /**
         * Run the migrations.
         */
        public function up(): void
        {
            Schema::create('test_case_change', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->integer('test_case_id')->default(0)->comment('测试用例ID');
                $table->integer('changed_by')->default(0)->comment('变更人ID');
                $table->string('change_type', 255)->default('')->comment('变更类型');
                $table->string('change_field', 255)->default('')->comment('变更属性');
                $table->text('old_value')->nullable()->comment('变更前的值');
                $table->text('new_value')->nullable()->comment('变更后的值');
                $table->dateTime('created_at')->nullable()->comment('创建时间');
                $table->dateTime('updated_at')->nullable()->comment('更新时间');
                $table->dateTime('deleted_at')->nullable()->comment('删除时间');
            });
        }

        /**
         * Reverse the migrations.
         */
        public function down(): void
        {
            Schema::dropIfExists('test_case_change');
        }
    }
