<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitCategoryProductTable extends Migration
{
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('product')) {
            Schema::table('product', function (Blueprint $table) {
                // 类型字段
                if (!Schema::hasColumn('product', 'category')) {
                    $table->char('category', 16)->default('')->comment('产品类型');
                }

                // 原销售系统中主键字段
                if (!Schema::hasColumn('product', 'origin_product_id')) {
                    $table->integer('origin_product_id')->default(0)->comment('原销售系统中主键');
                }

                // 附件
                if (!Schema::hasColumn('product', 'attachment')) {
                    $table->json('attachment')->nullable()->comment('附件列表');
                }
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
